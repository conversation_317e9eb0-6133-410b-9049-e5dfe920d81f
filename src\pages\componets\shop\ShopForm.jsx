import { useTranslation } from "react-i18next";
import useShopForm from "../../../hooks/shop/useShopForm.js";
import {regionsList} from "../../../constant/regionList.js";
import {Link, useSearchParams} from "react-router-dom";

export default function ShopForm(){
    const { t } = useTranslation('common');
    const [searchParams] = useSearchParams();
    const {formik} = useShopForm({tier:searchParams.get("tier")});

    return (
        <form onSubmit={formik.handleSubmit}>
            <div className="col">
                <div className="row">
                    <div className="col-12 col-md-6 mb-3">
                        <label
                            htmlFor="shopName"
                            className="auth_label text-uppercase mb-2">
                            {t('forms.shopName')}
                        </label>
                        <input
                            type="text"
                            name="shopName"
                            className="auth_input"
                            {...formik.getFieldProps('shopName')}
                        />
                        {formik.touched.shopName &&
                            <small className="main_footer_copy text-danger">{formik.errors.shopName}</small>}
                    </div>
                    <div className="col-12 col-md-6 mb-3">
                        <label
                            htmlFor="firstName"
                            className="auth_label text-uppercase mb-2">
                            {t('forms.firstName')}
                        </label>
                        <input
                            type="text"
                            name="firstName"
                            className="auth_input"
                            {...formik.getFieldProps('firstName')}
                        />
                        {formik.touched.firstName &&
                            <small className="main_footer_copy text-danger">{formik.errors.firstName}</small>}
                    </div>
                    <div className="col-12 col-md-6 mb-3">
                        <label
                            htmlFor="lastName"
                            className="auth_label text-uppercase mb-2">
                            {t('forms.lastName')}
                        </label>
                        <input
                            type="text"
                            name="lastName"
                            className="auth_input"
                            {...formik.getFieldProps('lastName')}
                        />
                        {formik.touched.lastName &&
                            <small className="main_footer_copy text-danger">{formik.errors.lastName}</small>}
                    </div>
                    <div className="col-12 col-md-6 mb-3">
                        <label
                            htmlFor="phoneNumber"
                            className="auth_label text-uppercase mb-2">
                            {t('forms.phone')}
                        </label>
                        <input
                            type="phoneNumber"
                            name="phoneNumber"
                            className="auth_input"
                            {...formik.getFieldProps('phoneNumber')}
                        />
                        {formik.touched.phoneNumber &&
                            <small className="main_footer_copy text-danger">{formik.errors.phoneNumber}</small>}
                    </div>
                </div>
                <div className="col mb-3">
                    <label
                        htmlFor="location"
                        className="auth_label text-uppercase mb-2">
                        {t('forms.location')}
                    </label>
                    <select className="col acct_sel" {...formik.getFieldProps('location')}>
                        {regionsList.map((item, index) => <option key={index} value={item._id}>{item.name}</option>)}
                    </select>
                </div>
                <div className="col mb-3">
                    <label
                        htmlFor="shopDescription"
                        className="auth_label text-uppercase mb-2">
                        {t('forms.describeProduct')}
                    </label>
                    <textarea
                        className="vb_contact_input h-auto"
                        id=""
                        cols="30"
                        rows="5"
                        {...formik.getFieldProps('shopDescription')}
                    />
                    {formik.touched.shopDescription &&
                        <small className="main_footer_copy text-danger">{formik.errors.shopDescription}</small>}
                </div>
            </div>
            <div className="col pro_info_cont mb-2">
                <p className="pro_info_head green d-flex gap-2 align-items-center mb-3">
                          <span className="icon">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                              <path
                                  fill="currentColor"
                                  fillRule="evenodd"
                                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12s4.477 10 10 10s10-4.477 10-10M12 6.25a.75.75 0 0 1 .75.75v6a.75.75 0 0 1-1.5 0V7a.75.75 0 0 1 .75-.75M12 17a1 1 0 1 0 0-2a1 1 0 0 0 0 2"
                                  clipRule="evenodd"></path>
                            </svg>
                          </span>
                    KYC Verification
                </p>
                <p className="pro_info_text mb-4">
                    We work together with Kycaid. Verify your ID within 15
                    seconds
                </p>
                <Link to="https://forms.kycaid.com/708525fa0acb864dd11aafe030f4389ffa30" target="_blank" className="acct_offer_btn3 text-white">
                    {t('forms.continue')}
                </Link>
            </div>
            <button disabled={formik.isSubmitting || !formik.isValid}
                    className={`col-12 col-md-auto auth_btn ${formik.isValid ? 'active' : ''}`}>
                {formik.isSubmitting ? t('forms.processing') : t('forms.submit')}
            </button>
        </form>
    )
}