import {Link} from "react-router-dom";
import { useTranslation } from "react-i18next";
import {clearAccessToken} from "../../../vbrae-utils/index.js";
import {useContext} from "react";
import {ModalContext} from "../../../store/ModalContext.jsx";

export default function WebGuest({user}){
    const { t } = useTranslation('common');
    const {OpenModal} = useContext(ModalContext);

    return (
        <div className="header_drop_cont position-absolute">
            <Link to={"/affiliate"}>
                <p className="header_drop_text d-flex gap-2 align-items-center">
                      <span className="icon">
                        <svg
                            width="1em"
                            height="1em"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            xmlns="http://www.w3.org/2000/svg">
                          <path
                              d="M13.2891 12.9615C13.2891 12.9615 15.632 10.6385 16.4266 9.82056C18.9415 7.36673 17.6882 2.31184 17.6882 2.31184C17.6882 2.31184 12.6501 1.06039 10.1925 3.57148C8.27556 5.43639 7.03038 6.72874 7.03038 6.72874C7.03038 6.72874 3.88465 6.07438 2.00049 7.95565L12.0357 18C13.9198 16.0942 13.2891 12.9615 13.2891 12.9615ZM12.0603 5.59998C12.2124 5.44788 12.3931 5.32722 12.592 5.24489C12.7909 5.16257 13.0041 5.12019 13.2194 5.12019C13.4347 5.12019 13.6479 5.16257 13.8468 5.24489C14.0457 5.32722 14.2264 5.44788 14.3786 5.59998C14.6076 5.82883 14.7634 6.12032 14.8265 6.4376C14.8896 6.75488 14.857 7.08371 14.7329 7.38253C14.6089 7.68134 14.3989 7.93672 14.1295 8.1164C13.8601 8.29607 13.5434 8.39197 13.2194 8.39197C12.8955 8.39197 12.5788 8.29607 12.3094 8.1164C12.04 7.93672 11.83 7.68134 11.7059 7.38253C11.5819 7.08371 11.5493 6.75488 11.6124 6.4376C11.6754 6.12032 11.8313 5.82883 12.0603 5.59998ZM2.81969 17.1821C4.33608 17.1389 5.78777 16.5591 6.91569 15.5462L4.45809 13.0923C2.81969 13.9103 2.81969 17.1821 2.81969 17.1821Z"/>
                        </svg>
                      </span>
                    Affiliate program
                </p>
            </Link>
            {user && <Link onClick={() => {
                clearAccessToken();
                window.location.assign("/");
            }}>
                <p className="header_drop_text d-flex gap-2 align-items-center">
                    {t('auth.logout')}
                </p>
            </Link>}

            {!user && <>
                <hr className="header_drop_line mt-2 mb-3"/>

                <p className="header_drop_head">{t('auth.welcome')}</p>

                <p
                    className="header_drop_text d-flex gap-2 align-items-center"
                    role="button"
                    onClick={() => OpenModal("login")}>
                    <span className="icon">
                      <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 24 24">
                        <path
                            fill="currentColor"
                            d="M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12c0 4.84 3.44 8.87 8 9.8V15H8v-3h2V9.5C10 7.57 11.57 6 13.5 6H16v3h-2c-.55 0-1 .45-1 1v2h3v3h-3v6.95c5.05-.5 9-4.76 9-9.95"></path>
                      </svg>
                    </span>
                    {t('auth.facebook')}
                </p>
                <p
                    className="header_drop_text d-flex gap-2 align-items-center"
                    role="button"
                    onClick={() => OpenModal("login")}>
                    <span className="icon">
                      <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 24 24">
                        <path
                            fill="currentColor"
                            d="M21.35 11.1h-9.17v2.73h6.51c-.33 3.81-3.5 5.44-6.5 5.44C8.36 19.27 5 16.25 5 12c0-4.1 3.2-7.27 7.2-7.27c3.09 0 4.9 1.97 4.9 1.97L19 4.72S16.56 2 12.1 2C6.42 2 2.03 6.8 2.03 12c0 5.05 4.13 10 10.22 10c5.35 0 9.25-3.67 9.25-9.09c0-1.15-.15-1.81-.15-1.81"></path>
                      </svg>
                    </span>
                    {t('auth.google')}
                </p>
                <p
                    className="header_drop_text d-flex gap-2 align-items-center"
                    role="button"
                    onClick={() => OpenModal("login")}>
                    <span className="icon">
                      <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 24 24">
                        <path
                            fill="currentColor"
                            d="M12 15c.81 0 1.5-.3 2.11-.89c.59-.61.89-1.3.89-2.11s-.3-1.5-.89-2.11C13.5 9.3 12.81 9 12 9s-1.5.3-2.11.89C9.3 10.5 9 11.19 9 12s.3 1.5.89 2.11c.61.59 1.3.89 2.11.89m0-13c2.75 0 5.1 1 7.05 2.95S22 9.25 22 12v1.45c0 1-.35 1.85-1 2.55c-.7.67-1.5 1-2.5 1c-1.2 0-2.19-.5-2.94-1.5c-1 1-2.18 1.5-3.56 1.5c-1.37 0-2.55-.5-3.54-1.46C7.5 14.55 7 13.38 7 12c0-1.37.5-2.55 1.46-3.54C9.45 7.5 10.63 7 12 7c1.38 0 2.55.5 3.54 1.46C16.5 9.45 17 10.63 17 12v1.45c0 .41.16.77.46 1.08s.65.47 1.04.47c.42 0 .77-.16 1.07-.47s.43-.67.43-1.08V12c0-2.19-.77-4.07-2.35-5.65S14.19 4 12 4s-4.07.77-5.65 2.35S4 9.81 4 12s.77 4.07 2.35 5.65S9.81 20 12 20h5v2h-5c-2.75 0-5.1-1-7.05-2.95S2 14.75 2 12s1-5.1 2.95-7.05S9.25 2 12 2"></path>
                      </svg>
                    </span>
                    {t('auth.email')}
                </p>

                <hr className="header_drop_line mt-2 mb-3"/>

                <p className="header_drop_head">{t('auth.noAccount')}</p>

                <p
                    className="header_drop_text d-flex gap-2 align-items-center"
                    role="button"
                    onClick={() => OpenModal("register")}>
                    {t('auth.signUpNow')}
                </p>
            </>}
        </div>
    )
}