{"name": "vbrae", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"ecommpay": "^0.1.7", "firebase": "^10.8.0", "formik": "^2.4.6", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "prop-types": "^15.8.1", "pusher-js": "^8.4.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.6.0", "react-query": "^3.39.3", "react-router-dom": "^6.26.1", "react-share": "^5.2.2", "react-timer-hook": "^3.0.8", "sweetalert2": "^11.15.3", "swiper": "^11.1.14", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-basic-ssl": "^1.2.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "vite": "^5.4.1"}}