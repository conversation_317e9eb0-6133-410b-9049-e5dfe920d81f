import { createContext, useContext, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { changeLanguage, getCurrentLanguage, getSupportedLanguages, getLanguageDisplayName } from '../i18n/index.js';

// Create Language Context
const LanguageContext = createContext();

// Language Provider Component
export const LanguageProvider = ({ children }) => {
  const { i18n } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState(getCurrentLanguage());
  const [isLoading, setIsLoading] = useState(false);

  // Update current language when i18n language changes
  useEffect(() => {
    const handleLanguageChange = (lng) => {
      setCurrentLanguage(lng);
      setIsLoading(false);
    };

    i18n.on('languageChanged', handleLanguageChange);
    
    return () => {
      i18n.off('languageChanged', handleLanguageChange);
    };
  }, [i18n]);

  // Change language function
  const switchLanguage = async (languageCode) => {
    if (languageCode === currentLanguage) return;
    
    setIsLoading(true);
    try {
      await changeLanguage(languageCode);
      setCurrentLanguage(languageCode);
    } catch (error) {
      console.error('Failed to change language:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Get available languages with display names
  const getAvailableLanguages = () => {
    return getSupportedLanguages().map(lng => ({
      code: lng,
      name: getLanguageDisplayName(lng),
      isActive: lng === currentLanguage
    }));
  };

  // Get current language display name
  const getCurrentLanguageDisplayName = () => {
    return getLanguageDisplayName(currentLanguage);
  };

  // Check if language is supported
  const isLanguageSupported = (languageCode) => {
    return getSupportedLanguages().includes(languageCode);
  };

  // Context value
  const contextValue = {
    currentLanguage,
    switchLanguage,
    getAvailableLanguages,
    getCurrentLanguageDisplayName,
    isLanguageSupported,
    isLoading,
    supportedLanguages: getSupportedLanguages()
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook to use Language Context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  
  return context;
};

// Export context for advanced usage
export default LanguageContext;
