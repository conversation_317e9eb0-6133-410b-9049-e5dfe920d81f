import { useLanguage } from '../../store/LanguageContext.jsx';
import CustomDropdown from '../../pages/componets/utility/CustomDropdown.jsx';

export default function LanguageSwitcher() {
  const { 
    currentLanguage, 
    switchLanguage, 
    getAvailableLanguages, 
    getCurrentLanguageDisplayName,
    isLoading 
  } = useLanguage();

  const availableLanguages = getAvailableLanguages();

  const handleLanguageChange = (languageCode) => {
    if (languageCode !== currentLanguage && !isLoading) {
      switchLanguage(languageCode);
    }
  };

  return (
    <CustomDropdown
      trigger={() => (
        <span className="main_footer_tag d-flex align-items-center gap-1" role="button">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="1em"
            height="1em"
            viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M8.478 21.439a10.1 10.1 0 0 1-6.504-8.573h4.32a17.54 17.54 0 0 0 2.184 8.573m7.505-8.573c-.182 5.465-2.174 9.208-3.973 9.208c-1.8 0-3.791-3.743-3.974-9.208zm6.043 0a10.06 10.06 0 0 1-6.514 8.573a17.43 17.43 0 0 0 2.194-8.573zm0-1.732h-4.32c.02-3-.735-5.952-2.194-8.573a10.05 10.05 0 0 1 6.514 8.573m-6.043 0H8.036c.183-5.475 2.174-9.208 3.974-9.208c1.799 0 3.781 3.733 3.973 9.208M8.478 2.61a17.5 17.5 0 0 0-2.184 8.572h-4.32A10.07 10.07 0 0 1 8.478 2.61"
            />
          </svg>
          {isLoading ? 'Loading...' : getCurrentLanguageDisplayName()}
        </span>
      )}
      content={
        <div className="footer_drop_cont foot position-absolute">
          {availableLanguages.map((language) => (
            <p
              key={language.code}
              className={`header_drop_cate ${language.isActive ? 'active' : ''} d-flex gap-2 align-items-center position-relative`}
              role="button"
              onClick={() => handleLanguageChange(language.code)}
              style={{ 
                cursor: isLoading ? 'not-allowed' : 'pointer',
                opacity: isLoading ? 0.6 : 1 
              }}
            >
              {language.isActive && (
                <span className="icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="1em"
                    height="1em"
                    viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="m9.55 18l-5.7-5.7l1.425-1.425L9.55 15.15l9.175-9.175L20.15 7.4z"></path>
                  </svg>
                </span>
              )}
              {language.name}
            </p>
          ))}
        </div>
      }
    />
  );
}
