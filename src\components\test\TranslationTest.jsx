import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../store/LanguageContext.jsx';

export default function TranslationTest() {
  const { t } = useTranslation(['common', 'pages']);
  const { currentLanguage, getAvailableLanguages, switchLanguage } = useLanguage();

  const testTranslations = () => {
    console.log('=== Translation Test Results ===');
    console.log('Current Language:', currentLanguage);
    console.log('Available Languages:', getAvailableLanguages());
    
    // Test common translations
    console.log('\n--- Common Translations ---');
    console.log('Navigation Home:', t('navigation.home'));
    console.log('Footer Copyright:', t('footer.copyright'));
    console.log('Auth Welcome:', t('auth.welcome'));
    console.log('Newsletter Title:', t('newsletter.title'));
    
    // Test page translations
    console.log('\n--- Page Translations ---');
    console.log('Home Title:', t('pages:home.title'));
    console.log('Home Latest Articles:', t('pages:home.latestArticles'));
    console.log('Category Title:', t('pages:category.title'));
    console.log('Cart Title:', t('pages:cart.title'));
    
    console.log('=== End Translation Test ===\n');
  };

  const testLanguageSwitch = async (langCode) => {
    console.log(`Switching to ${langCode}...`);
    await switchLanguage(langCode);
    setTimeout(() => {
      testTranslations();
    }, 100);
  };

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: '#1B2435', 
      color: 'white', 
      padding: '10px', 
      borderRadius: '5px',
      zIndex: 9999,
      fontSize: '12px',
      maxWidth: '300px'
    }}>
      <h4>Translation Test Panel</h4>
      <p>Current: {currentLanguage}</p>
      
      <div style={{ marginBottom: '10px' }}>
        <button 
          onClick={testTranslations}
          style={{ 
            background: '#1095ED', 
            color: 'white', 
            border: 'none', 
            padding: '5px 10px', 
            borderRadius: '3px',
            marginRight: '5px',
            fontSize: '11px'
          }}
        >
          Test Current
        </button>
      </div>

      <div>
        <p>Switch Language:</p>
        {getAvailableLanguages().map(lang => (
          <button
            key={lang.code}
            onClick={() => testLanguageSwitch(lang.code)}
            style={{ 
              background: lang.isActive ? '#28a745' : '#6c757d', 
              color: 'white', 
              border: 'none', 
              padding: '3px 8px', 
              borderRadius: '3px',
              margin: '2px',
              fontSize: '10px'
            }}
          >
            {lang.code.toUpperCase()}
          </button>
        ))}
      </div>

      <div style={{ marginTop: '10px' }}>
        <p>Sample Translations:</p>
        <div style={{ fontSize: '10px' }}>
          <div>Home: {t('navigation.home')}</div>
          <div>Cart: {t('navigation.cart')}</div>
          <div>Subscribe: {t('newsletter.subscribe')}</div>
        </div>
      </div>
    </div>
  );
}
