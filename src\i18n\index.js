import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import enCommon from '../locales/en/common.json';
import enPages from '../locales/en/pages.json';
import esCommon from '../locales/es/common.json';
import esPages from '../locales/es/pages.json';
import frCommon from '../locales/fr/common.json';
import frPages from '../locales/fr/pages.json';
import lvCommon from '../locales/lv/common.json';
import lvPages from '../locales/lv/pages.json';
import plCommon from '../locales/pl/common.json';
import plPages from '../locales/pl/pages.json';

// Language resources
const resources = {
  en: {
    common: enCommon,
    pages: enPages
  },
  es: {
    common: esCommon,
    pages: esPages
  },
  fr: {
    common: frCommon,
    pages: frPages
  },
  lv: {
    common: lvCommon,
    pages: lvPages
  },
  pl: {
    common: plCommon,
    pages: plPages
  }
};

// Language configuration
const languageConfig = {
  // Supported languages
  supportedLngs: ['en', 'es', 'fr', 'lv', 'pl'],
  
  // Default language
  fallbackLng: 'en',
  
  // Default namespace
  defaultNS: 'common',
  
  // Available namespaces
  ns: ['common', 'pages'],
  
  // Language detection options
  detection: {
    // Order of language detection methods
    order: ['localStorage', 'navigator', 'htmlTag'],
    
    // Cache user language
    caches: ['localStorage'],
    
    // Key to store language in localStorage
    lookupLocalStorage: 'vbrae-language',
    
    // Don't lookup from subdomain
    lookupFromSubdomainIndex: 0,
    
    // Don't lookup from path
    lookupFromPathIndex: 0,
    
    // Check all fallback languages
    checkWhitelist: true
  },
  
  // Interpolation options
  interpolation: {
    escapeValue: false // React already escapes values
  },
  
  // React options
  react: {
    useSuspense: false // Disable suspense for SSR compatibility
  }
};

// Initialize i18next
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    ...languageConfig,
    resources,
    
    // Debug mode (disable in production)
    debug: import.meta.env.DEV,
    
    // Return key if translation is missing
    returnKeyIfMissing: true,
    
    // Return empty string for missing translations
    returnEmptyString: false,
    
    // Keyseparator
    keySeparator: '.',
    
    // Namespace separator
    nsSeparator: ':',
    
    // Pluralization
    pluralSeparator: '_',
    contextSeparator: '_'
  });

// Export language utilities
export const changeLanguage = (lng) => {
  return i18n.changeLanguage(lng);
};

export const getCurrentLanguage = () => {
  return i18n.language;
};

export const getSupportedLanguages = () => {
  return languageConfig.supportedLngs;
};

export const getLanguageDisplayName = (lng) => {
  const displayNames = {
    en: 'English (US)',
    es: 'Español',
    fr: 'Français',
    lv: 'Latviešu',
    pl: 'Polski'
  };
  return displayNames[lng] || lng;
};

export default i18n;
