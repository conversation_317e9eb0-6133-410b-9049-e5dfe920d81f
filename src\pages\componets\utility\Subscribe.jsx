/* eslint-disable react/prop-types */
import { useTranslation } from "react-i18next";
import useNewsletterForm from "../../../hooks/newsletter/useNewsletterForm";

export default function Subscribe({customClass}) {
  const { t } = useTranslation('common');
  const {formik} = useNewsletterForm();

  return (
    <>
      <div
        className={"main_sub_con " + (customClass ? customClass : "")}
        style={{
          backgroundImage: "url(./assets/images/sub_bg.png)",
        }}>
        <div className="main_sub_cont d-lg-flex align-items-center gap-3 ">
          <div className="col">
            <h4 className="main_sub_title">{t('newsletter.title')}</h4>
            <p className="main_sub_text">
              {t('newsletter.description')}
            </p>
          </div>
          <form onSubmit={formik.handleSubmit} className="col d-flex gap-3 mt-3 mt-lg-0">
              <input
                type="email"
                className="main_input"
                placeholder={t('newsletter.emailPlaceholder')}
                {...formik.getFieldProps('email')}
              />
              <button type="submit" className="main_button" disabled={formik.isSubmitting || !formik.isValid}>
                  {formik.isSubmitting ? t('newsletter.processing') : t('newsletter.subscribe')}
              </button>
          </form>
        </div>
      </div>
    </>
  );
}
