import {Link, useSearchParams} from "react-router-dom";
import { useTranslation } from "react-i18next";
import Pagination from "./componets/utility/Pagination";
import Subscribe from "./componets/utility/Subscribe";
import MenuSideBar from "./componets/MenuSideBar";
import MainHeader from "./componets/MainHeader";
import BlogFilter from "./componets/BlogFilter";
import RightSideBar from "./componets/RightSideBar";
import MainFooter from "./componets/MainFooter";
import {useBlogs} from "../hooks/blogs/useBlogs.js";
import Spinner from "../components/common/Spinner.jsx";
import {useState} from "react";
import {generateQuery} from "../vbrae-utils/lib/misc.js";
import {convertToDays} from "../vbrae-utils/lib/time.js";
import MobileFilters from "../components/blog/MobileFilters.jsx";

export default function Blog() {
  const { t } = useTranslation('pages');
  const [searchParams] = useSearchParams();
  const [activeFilter, setActiveFilter] = useState({
    tag: searchParams.get("tag") ?? "", category: searchParams.get("category") ?? "",
  });
  return (
    <>
      <div className="d-flex main_house_con">
        <MenuSideBar makeShort={true} activeLink={"blogs"} />

        <div
          className="col housing_con d-flex flex-column"
          style={
            {
              // backgroundImage: " url('./assets/images/psn_bg.png')",
            }
          }>
          <MainHeader activeLink={"blogs"} />

          <div className="housing d-flex gap-1 position-relative">
            <div id="scrollable-section" className="col main_section">
              <div className="d-flex align-items-center gap-2 mb-3">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                <Link>
                  <p className="crumb_link">/ Blog</p>
                </Link>
              </div>

              <div className="main_cont d-flex gap-4">
                <BlogFilter activeFilter={activeFilter} setActiveFilter={setActiveFilter}/>

                <div className="col">
                  <p className="blog_head mb-2">{t('blog.welcome')}</p>
                  <p className="blog_title">{t('blog.title')}</p>

                  <hr className="blog_line d-none d-lg-block my-4"/>
                  <MobileFilters setActiveFilter={setActiveFilter}/>
                  <RenderBlogs activeFilter={activeFilter}/>
                </div>
              </div>

              <div className="col d-lg-none">
                <MainFooter/>
              </div>
            </div>

            <RightSideBar/>
          </div>
        </div>
      </div>
    </>
  );
}

const RenderBlogs = ({activeFilter}) => {
  const { t } = useTranslation('pages');
  const [page, setPage] = useState(1);
  const {blogs, blogsLoading} = useBlogs({query: generateQuery({
      selectedCategory: activeFilter.category,
      staticString: `${activeFilter.tag ? `tags=${activeFilter.tag}` : ''}`
    })});

  if(!blogs || blogsLoading) return <Spinner />;

  if(!blogs.data[0]) return <p className="cart_item_name text-center mt-5">Nothing here yet</p>

  return (
      <>
        <div className="col d-md-flex d-lg-block d-lg-flex gap-4 mb-5">
          <div className="col col-md-7 col-lg-6 col-xl-8 mb-3 mb-md-0">
            <img
                src={blogs.data[0].image}
                alt=""
                className="blog_img_bg"
            />
          </div>
          <div className="col my-auto">
            <p className="blog_item_head mb-2">
              {blogs.data[0].keywords[0]} • {convertToDays(blogs.data[0].createdAt)}
            </p>
            <p className="blog_item_name_bg mb-2">
              {blogs.data[0].title}
            </p>
            <p className="blog_item_text mb-4 mb-md-5">
              {blogs.data[0].summary}
            </p>
            <Link to={`/blog-details/${blogs.data[0]._id}`}>
              <span className="blog_item_link">{t('blog.readMore')}</span>
            </Link>
          </div>
        </div>

        {/* Subscribe Section */}
        <div className="col mb-5">
          <Subscribe/>
        </div>

        <div className="col row g-3 mb-4">
          {blogs.data.slice(1).map(blog => (
              <div className="col-12 col-md-6 col-xl-4 blog_item" key={blog._id}>
                <img
                    src={blog.image}
                    alt=""
                    className="blog_item_img mb-3"
                />
                <p className="blog_item_head mb-2">
                  {blog.keywords[0]} • {convertToDays(blog.createdAt)}
                </p>
                <Link to={`/blog-details/${blog._id}`}>
                  <p className="blog_item_name mb-2">
                    {blog.title}
                  </p>
                </Link>
                <p className="blog_item_text mb-3">
                  {blog.summary}
                </p>
              </div>
          ))}
        </div>

        <div className="col d-flex justify-content-center">
          {blogs.pagination.pages && <Pagination
              totalPages={blogs.pagination.pages}
              currentPage={page}
              pageClick={(page) => setPage(page)}
              nextPage={() => setPage(page + 1)}
              prevPage={() => setPage(page - 1)}
          />}
        </div>
      </>
  )
}